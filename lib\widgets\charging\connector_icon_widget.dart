import 'package:flutter/material.dart';

class ConnectorIconWidget extends StatelessWidget {
  final String connectorType;
  final double size;
  final Color? color;
  final String? status; // Add status parameter for color logic

  const ConnectorIconWidget({
    super.key,
    required this.connectorType,
    this.size = 24.0,
    this.color,
    this.status, // Optional status for automatic color selection
  });

  // Color constants for connector status
  static const Color _chargingLimeGreen = Color(0xFF8cc051); // Lime green for charging connectors
  static const Color _gunConnectedYellow = Color(0xFFFFEB3B); // Yellow for gun connected
  static const Color _unifiedBadgeColor = Color(0xFF1E88E5); // Blue for other statuses

  /// Get connector color based on status with priority logic
  /// Priority: charging (lime green) > gun connected (yellow) > default (blue)
  Color _getConnectorColor(String? status) {
    if (status == null || status.isEmpty) {
      return _unifiedBadgeColor; // Default blue color
    }

    final statusLower = status.toLowerCase().trim();

    // Priority 1: If status is "charging" - use lime green
    if (statusLower == 'charging') {
      return _chargingLimeGreen;
    }

    // Priority 2: If status is "gun connected" - use yellow
    if (statusLower == 'gun connected') {
      return _gunConnectedYellow;
    }

    // Priority 3: For all other statuses - use blue
    return _unifiedBadgeColor;
  }

  @override
  Widget build(BuildContext context) {
    // Use provided color or determine from status
    final effectiveColor = color ?? _getConnectorColor(status);

    return SizedBox(
      width: size,
      height: size,
      child: _getConnectorIcon(effectiveColor),
    );
  }

  Widget _getConnectorIcon(Color effectiveColor) {
    String iconPath = 'assets/images/connector_icons/';

    // Determine the correct connector icon based on type
    switch (connectorType.toLowerCase()) {
      case 'ccs2':
      case 'ccs':
      case 'ccs combo':
      case 'ccs_combo':
        iconPath += 'ccs2.png';
        break;
      case 'chademo':
        iconPath += 'chademo.png';
        break;
      case 'type2':
      case 'type 2':
        iconPath += 'type2.png';
        break;
      case 'gbt':
      case 'gb/t':
        iconPath += 'gbt.png';
        break;
      default:
        iconPath += 'generic.png';
    }

    return Image.asset(
      iconPath,
      width: size,
      height: size,
      color: effectiveColor, // Use the status-based color
    );
  }
}
