import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../models/station.dart';
import '../utils/app_themes.dart';

class StationListSheetRiverpod extends ConsumerStatefulWidget {
  final DraggableScrollableController sheetController;
  final TextEditingController searchController;
  final AsyncValue<List<Station>> stations;
  final List<Station> filteredStations;
  final bool isSearching;
  final bool isSearchLoading;
  final Function(BuildContext) showFilterOptions;
  final Function(Station) buildStationCard;
  final VoidCallback onSearchChanged;
  final LatLng? initialPosition;
  final Function(Station) onMarkerTapped;
  final VoidCallback onBackPressed;
  final double minSheetSize;
  final double midSheetSize;
  final double maxSheetSize;
  final bool showingNearestStations;
  final VoidCallback? onRefresh;
  final Function(Map<String, bool>, bool)? onFilter;

  const StationListSheetRiverpod({
    super.key,
    required this.sheetController,
    required this.searchController,
    required this.stations,
    required this.filteredStations,
    required this.isSearching,
    this.isSearchLoading = false,
    required this.showFilterOptions,
    required this.buildStationCard,
    required this.onSearchChanged,
    this.initialPosition,
    required this.onMarkerTapped,
    required this.onBackPressed,
    this.minSheetSize = 0.28,
    this.midSheetSize = 0.4,
    this.maxSheetSize = 0.85,
    this.showingNearestStations = false,
    this.onRefresh,
    this.onFilter,
  });

  @override
  ConsumerState<StationListSheetRiverpod> createState() =>
      _StationListSheetRiverpodState();
}

class _StationListSheetRiverpodState
    extends ConsumerState<StationListSheetRiverpod> {
  late ValueNotifier<double> _positionNotifier;

  @override
  void initState() {
    super.initState();
    _positionNotifier = ValueNotifier<double>(widget.sheetController.isAttached
        ? widget.sheetController.size
        : widget.minSheetSize);

    if (widget.sheetController.isAttached) {
      widget.sheetController.addListener(_updatePositionNotifier);
    }
  }

  void _updatePositionNotifier() {
    if (widget.sheetController.isAttached) {
      _positionNotifier.value = widget.sheetController.size;
    }
  }

  @override
  void dispose() {
    if (widget.sheetController.isAttached) {
      widget.sheetController.removeListener(_updatePositionNotifier);
    }
    _positionNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get the keyboard height to adjust the sheet
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    return DraggableScrollableSheet(
      controller: widget.sheetController,
      initialChildSize: widget.midSheetSize,
      minChildSize: widget.minSheetSize,
      maxChildSize: widget.maxSheetSize,
      snap: true,
      snapSizes: [
        widget.minSheetSize,
        widget.midSheetSize,
        widget.maxSheetSize
      ],
      builder: (context, scrollController) {
        return NotificationListener<ScrollNotification>(
          onNotification: (notification) {
            // Handle scroll notifications for better physics
            if (notification is ScrollEndNotification) {
              if (scrollController.hasClients) {
                final maxScroll = scrollController.position.maxScrollExtent;
                final currentScroll = scrollController.position.pixels;
                final viewportDimension =
                    scrollController.position.viewportDimension;

                if (currentScroll > 0 && maxScroll < viewportDimension) {
                  scrollController.animateTo(
                    0,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOutCubic,
                  );
                } else if (currentScroll < maxScroll) {
                  final remainingScrollableDistance = maxScroll - currentScroll;

                  if (remainingScrollableDistance < viewportDimension * 0.2) {
                    scrollController.animateTo(
                      maxScroll,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOutCubic,
                    );
                  }
                }
              }
            }
            return false;
          },
          child: ValueListenableBuilder<double>(
            valueListenable: _positionNotifier,
            builder: (context, sheetSize, child) {
              return Theme(
                data: ThemeData.light().copyWith(
                  primaryColor: Colors.black,
                  colorScheme: const ColorScheme.light(
                    primary: Colors.black,
                    secondary: Colors.black,
                  ),
                  cardTheme: CardThemeData(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                  ),
                  textTheme: const TextTheme(
                    titleLarge: TextStyle(
                        color: Colors.black87, fontWeight: FontWeight.w600),
                    titleMedium: TextStyle(
                        color: Colors.black87, fontWeight: FontWeight.w500),
                    bodyLarge: TextStyle(color: Colors.black87),
                    bodyMedium: TextStyle(color: Colors.black54),
                  ),
                ),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOutCubic,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(20),
                      bottom: Radius.zero,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(25),
                        blurRadius: 10,
                        spreadRadius: 2,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  padding: EdgeInsets.only(
                      bottom: isKeyboardVisible ? keyboardHeight : 0),
                  child: Column(
                    children: [
                      _buildSheetHeader(context),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: _buildCurvedSearchBar(context),
                      ),
                      Expanded(
                        child: _buildStationListContent(
                          context,
                          scrollController,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildSheetHeader(BuildContext context) {
    // Make the handle area fully draggable with no extra spacing
    return GestureDetector(
      behavior:
          HitTestBehavior.opaque, // Important: Makes entire area draggable
      onVerticalDragUpdate: (details) {
        if (widget.sheetController.isAttached) {
          // Calculate new sheet size based on drag
          final newSize = widget.sheetController.size -
              (details.delta.dy / MediaQuery.of(context).size.height);

          // Clamp the size between min and max
          final clampedSize =
              newSize.clamp(widget.minSheetSize, widget.maxSheetSize);

          // Update the sheet position
          widget.sheetController.jumpTo(clampedSize);
        }
      },
      onVerticalDragEnd: (details) {
        if (!widget.sheetController.isAttached) return;

        final velocity = details.primaryVelocity ?? 0;
        if (velocity < -300) {
          // Swipe up - expand
          widget.sheetController.animateTo(
            widget.maxSheetSize,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        } else if (velocity > 300) {
          // Swipe down - collapse
          widget.sheetController.animateTo(
            widget.minSheetSize,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        } else {
          // Snap to closest position
          final currentSize = widget.sheetController.size;
          if (currentSize < (widget.minSheetSize + widget.midSheetSize) / 2) {
            widget.sheetController.animateTo(
              widget.minSheetSize,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          } else if (currentSize <
              (widget.midSheetSize + widget.maxSheetSize) / 2) {
            widget.sheetController.animateTo(
              widget.midSheetSize,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          } else {
            widget.sheetController.animateTo(
              widget.maxSheetSize,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        }
      },
      onTap: () {
        // Toggle between min and max size when tapping the handle
        if (widget.sheetController.isAttached) {
          final currentSize = widget.sheetController.size;
          final targetSize = currentSize < widget.maxSheetSize * 0.7
              ? widget.maxSheetSize
              : widget.minSheetSize;

          // Animate to the target size
          widget.sheetController.animateTo(
            targetSize,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      },
      child: Container(
        width: double.infinity,
        height: 30, // Reduced height to minimize spacing
        color: Colors.transparent,
        alignment: Alignment.center,
        child: Container(
          width: 50,
          height: 5, // Handle indicator
          decoration: BoxDecoration(
            color: Colors.grey.withAlpha(200),
            borderRadius: BorderRadius.circular(2.5),
          ),
        ),
      ),
    );
  }

  Widget _buildCurvedSearchBar(BuildContext context) {
    return GestureDetector(
      onVerticalDragUpdate: (details) {
        if (widget.sheetController.isAttached) {
          final newSize = widget.sheetController.size -
              (details.delta.dy / MediaQuery.of(context).size.height);
          if (newSize >= widget.minSheetSize &&
              newSize <= widget.maxSheetSize) {
            widget.sheetController.jumpTo(newSize);
          }
        }
      },
      onVerticalDragEnd: (details) {
        if (!widget.sheetController.isAttached) return;

        final velocity = details.primaryVelocity ?? 0;
        if (velocity < -300) {
          widget.sheetController.animateTo(
            widget.maxSheetSize,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        } else if (velocity > 300) {
          widget.sheetController.animateTo(
            widget.minSheetSize,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        } else {
          final currentSize = widget.sheetController.size;
          if (currentSize < (widget.minSheetSize + widget.midSheetSize) / 2) {
            widget.sheetController.animateTo(
              widget.minSheetSize,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          } else if (currentSize <
              (widget.midSheetSize + widget.maxSheetSize) / 2) {
            widget.sheetController.animateTo(
              widget.midSheetSize,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          } else {
            widget.sheetController.animateTo(
              widget.maxSheetSize,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        }
      },
      child: TweenAnimationBuilder<double>(
        tween: Tween<double>(begin: 0.8, end: 1.0),
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOutCubic,
        builder: (context, value, child) {
          final isDarkMode = Theme.of(context).brightness == Brightness.dark;
          const primaryColor = Color(0xFF67C44C);

          return Transform.scale(
            scale: value,
            child: Container(
              height: 56, // Increased height for better touch targets
              decoration: BoxDecoration(
                // Modern gradient background
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: isDarkMode
                      ? [
                          const Color(0xFF2C2C2C),
                          const Color(0xFF2C2C2C).withAlpha(240),
                        ]
                      : [
                          Colors.white,
                          const Color(0xFFFAFBFF),
                        ],
                ),
                borderRadius: BorderRadius.circular(16), // More rounded corners
                border: Border.all(
                  color: isDarkMode
                      ? Colors.grey.shade700.withAlpha(100)
                      : primaryColor.withAlpha(30),
                  width: 1.5,
                ),
                boxShadow: [
                  // Enhanced shadow for depth
                  BoxShadow(
                    color: isDarkMode
                        ? Colors.black.withAlpha(40)
                        : primaryColor.withAlpha(15),
                    blurRadius: isDarkMode ? 12 : 8,
                    spreadRadius: 0,
                    offset: const Offset(0, 4),
                  ),
                  // Inner highlight for glass effect
                  BoxShadow(
                    color: isDarkMode
                        ? Colors.white.withAlpha(5)
                        : Colors.white.withAlpha(80),
                    blurRadius: 1,
                    spreadRadius: 0,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                children: [
                  const SizedBox(width: 20), // Increased padding
                  // Enhanced search icon with background
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? primaryColor.withAlpha(20)
                          : primaryColor.withAlpha(10),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      child: widget.isSearchLoading
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2.5,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  primaryColor,
                                ),
                              ),
                            )
                          : Icon(
                              Icons.search_rounded,
                              color: primaryColor,
                              size: 20,
                            ),
                    ),
                  ),
                  const SizedBox(width: 16), // Increased spacing
                  // Enhanced search text field
                  Expanded(
                    child: TextField(
                      controller: widget.searchController,
                      autofocus: false,
                      decoration: InputDecoration(
                        hintText: 'Search...',
                        hintStyle: TextStyle(
                          color: isDarkMode
                              ? Colors.grey.shade500
                              : Colors.grey.shade500,
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                        ),
                        border: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        errorBorder: InputBorder.none,
                        focusedErrorBorder: InputBorder.none,
                        contentPadding:
                            const EdgeInsets.symmetric(vertical: 16),
                      ),
                      style: TextStyle(
                        color: isDarkMode ? Colors.white : Colors.black87,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      onChanged: (_) => widget.onSearchChanged(),
                      onTap: () {
                        if (widget.sheetController.isAttached) {
                          widget.sheetController.animateTo(
                            widget.maxSheetSize,
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeOut,
                          );
                        }
                      },
                    ),
                  ),
                  // Enhanced clear button when text is entered
                  if (widget.searchController.text.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: Material(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(20),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(20),
                          onTap: () {
                            widget.searchController.clear();
                            widget.onSearchChanged();
                          },
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: isDarkMode
                                  ? Colors.grey.shade700.withAlpha(100)
                                  : Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Icon(
                              Icons.close_rounded,
                              size: 16,
                              color: isDarkMode
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  // Enhanced divider
                  Container(
                    height: 32,
                    width: 1.5,
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          isDarkMode
                              ? Colors.grey.shade700
                              : Colors.grey.shade300,
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                  // Enhanced filter button with modern design
                  Container(
                    margin: const EdgeInsets.only(right: 2),
                    child: Material(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(14),
                      child: InkWell(
                        onTap: () => widget.showFilterOptions(context),
                        borderRadius: BorderRadius.circular(14),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: isDarkMode
                                  ? [
                                      primaryColor.withAlpha(40),
                                      primaryColor.withAlpha(20),
                                    ]
                                  : [
                                      primaryColor.withAlpha(15),
                                      primaryColor.withAlpha(25),
                                    ],
                            ),
                            borderRadius: BorderRadius.circular(14),
                            border: Border.all(
                              color: primaryColor.withAlpha(60),
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: primaryColor.withAlpha(20),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: primaryColor.withAlpha(20),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Icon(
                                  Icons.filter_alt_rounded,
                                  color: primaryColor,
                                  size: 16,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Filter',
                                style: TextStyle(
                                  color: primaryColor,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 14,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStationListContent(
      BuildContext context, ScrollController scrollController) {
    // Handle searching and filtered stations if provided
    if (widget.isSearching) {
      if (widget.isSearchLoading) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
              ),
              const SizedBox(height: 16),
              Text(
                'Searching for "${widget.searchController.text}"...',
                style: TextStyle(color: Colors.grey.shade600),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      if (widget.filteredStations.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.search_off, size: 64, color: Colors.grey.shade400),
              const SizedBox(height: 16),
              Text(
                'No stations found matching "${widget.searchController.text}"',
                style: TextStyle(color: Colors.grey.shade600),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return _buildStationListView(widget.filteredStations, scrollController);
    }

    // Handle the AsyncValue state for stations
    return widget.stations.when(
      loading: () => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(
              width: 40,
              height: 40,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF67C44C)),
                strokeWidth: 3,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Loading stations...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'Connection Problem',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (widget.onRefresh != null)
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: ElevatedButton.icon(
                  onPressed: widget.onRefresh,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF67C44C),
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
          ],
        ),
      ),
      data: (stations) {
        if (stations.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.location_off, size: 64, color: Colors.grey.shade400),
                const SizedBox(height: 16),
                const Text(
                  'No stations found nearby',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Try selecting a different location',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return _buildStationListView(stations, scrollController);
      },
    );
  }

  Widget _buildStationListView(
      List<Station> stationsList, ScrollController scrollController) {
    return RefreshIndicator(
      onRefresh: () async {
        if (widget.onRefresh != null) {
          widget.onRefresh!();
        }
      },
      color: AppThemes.primaryColor,
      child: ListView.builder(
        controller: scrollController,
        itemCount: stationsList.length,
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        itemBuilder: (context, index) {
          final station = stationsList[index];
          // Add logging to debug station data
          debugPrint(
              'Rendering station at index $index: ${station.name}, UID: ${station.uid}');
          // Station should never be null here, but log key properties for debugging
          debugPrint(
              'Station details - Name: ${station.name}, Status: ${station.status}');
          return widget.buildStationCard(station);
        },
      ),
    );
  }
}
