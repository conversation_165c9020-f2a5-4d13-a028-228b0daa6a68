import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ecoplug/services/payment/payu_lifecycle_manager.dart';
import 'package:ecoplug/services/payment/payu_service.dart';

void main() {
  group('PayU Lifecycle Manager Tests', () {
    late PayULifecycleManager lifecycleManager;

    setUp(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      lifecycleManager = PayULifecycleManager.instance;
    });

    tearDown(() async {
      // Clean up after each test
      await lifecycleManager.clearPendingPayment();
    });

    test('Test 1: Store and retrieve pending payment data', () async {
      print('\n🔔 ===== TEST 1: STORE AND RETRIEVE PENDING PAYMENT =====');
      
      const testTransactionId = 'TEST_TXN_123';
      const testMerchantTxnId = 'MERCHANT_123';
      const testAmount = 100.0;
      final testPaymentParams = {
        'txnid': testTransactionId,
        'amount': testAmount.toString(),
        'productinfo': 'Test Product',
        'firstname': 'Test User',
        'email': '<EMAIL>',
      };

      // Store pending payment
      await lifecycleManager.storePendingPayment(
        transactionId: testTransactionId,
        merchantTxnId: testMerchantTxnId,
        amount: testAmount,
        paymentParams: testPaymentParams,
      );

      // Check if pending payment exists
      final hasPending = await lifecycleManager.hasPendingPayment();
      expect(hasPending, isTrue, reason: 'Should have pending payment after storing');

      // Retrieve pending payment data
      final paymentData = await lifecycleManager.getPendingPaymentData();
      expect(paymentData, isNotNull, reason: 'Payment data should not be null');
      expect(paymentData!['transactionId'], equals(testTransactionId));
      expect(paymentData['merchantTxnId'], equals(testMerchantTxnId));
      expect(paymentData['amount'], equals(testAmount));

      print('✅ TEST 1 PASSED: Store and retrieve pending payment data');
    });

    test('Test 2: Clear pending payment data', () async {
      print('\n🔔 ===== TEST 2: CLEAR PENDING PAYMENT DATA =====');
      
      // Store some test data first
      await lifecycleManager.storePendingPayment(
        transactionId: 'TEST_CLEAR_123',
        merchantTxnId: 'MERCHANT_CLEAR_123',
        amount: 50.0,
        paymentParams: {'test': 'data'},
      );

      // Verify data exists
      final hasDataBefore = await lifecycleManager.hasPendingPayment();
      expect(hasDataBefore, isTrue, reason: 'Should have data before clearing');

      // Clear the data
      await lifecycleManager.clearPendingPayment();

      // Verify data is cleared
      final hasDataAfter = await lifecycleManager.hasPendingPayment();
      expect(hasDataAfter, isFalse, reason: 'Should not have data after clearing');

      final paymentDataAfter = await lifecycleManager.getPendingPaymentData();
      expect(paymentDataAfter, isNull, reason: 'Payment data should be null after clearing');

      print('✅ TEST 2 PASSED: Clear pending payment data');
    });

    test('Test 3: Payment expiration handling', () async {
      print('\n🔔 ===== TEST 3: PAYMENT EXPIRATION HANDLING =====');
      
      // Store payment data with a past timestamp (simulate old payment)
      final prefs = await SharedPreferences.getInstance();
      
      // Set a timestamp that's older than 10 minutes (payment should expire)
      final oldTimestamp = DateTime.now().subtract(const Duration(minutes: 15)).millisecondsSinceEpoch;
      
      await prefs.setBool('payu_pending_payment', true);
      await prefs.setInt('payu_payment_start_time', oldTimestamp);
      await prefs.setString('payu_transaction_data', '{"transactionId": "EXPIRED_123"}');

      // Check if payment is considered pending (should be false due to expiration)
      final hasPending = await lifecycleManager.hasPendingPayment();
      expect(hasPending, isFalse, reason: 'Expired payment should not be considered pending');

      // Verify data was automatically cleaned up
      final paymentData = await lifecycleManager.getPendingPaymentData();
      expect(paymentData, isNull, reason: 'Expired payment data should be cleaned up');

      print('✅ TEST 3 PASSED: Payment expiration handling');
    });

    test('Test 4: Mark payment as completed', () async {
      print('\n🔔 ===== TEST 4: MARK PAYMENT AS COMPLETED =====');
      
      // Store pending payment
      await lifecycleManager.storePendingPayment(
        transactionId: 'COMPLETE_TEST_123',
        merchantTxnId: 'MERCHANT_COMPLETE_123',
        amount: 75.0,
        paymentParams: {'test': 'completion'},
      );

      // Verify payment is pending
      final hasPendingBefore = await lifecycleManager.hasPendingPayment();
      expect(hasPendingBefore, isTrue, reason: 'Should have pending payment before completion');

      // Create a success result and mark as completed
      final successResult = PayUPaymentResult.success({'status': 'success', 'amount': '75.0'});
      await lifecycleManager.markPaymentCompleted(successResult);

      // Verify payment is no longer pending
      final hasPendingAfter = await lifecycleManager.hasPendingPayment();
      expect(hasPendingAfter, isFalse, reason: 'Should not have pending payment after completion');

      print('✅ TEST 4 PASSED: Mark payment as completed');
    });

    test('Test 5: Handle app resume with no pending payments', () async {
      print('\n🔔 ===== TEST 5: APP RESUME WITH NO PENDING PAYMENTS =====');
      
      // Ensure no pending payments
      await lifecycleManager.clearPendingPayment();

      // Handle app resume
      final result = await lifecycleManager.handleAppResume();

      // Should return null when no pending payments
      expect(result, isNull, reason: 'Should return null when no pending payments');

      print('✅ TEST 5 PASSED: App resume with no pending payments');
    });

    test('Test 6: Singleton instance consistency', () async {
      print('\n🔔 ===== TEST 6: SINGLETON INSTANCE CONSISTENCY =====');
      
      // Get multiple instances
      final instance1 = PayULifecycleManager.instance;
      final instance2 = PayULifecycleManager.instance;

      // Should be the same instance
      expect(identical(instance1, instance2), isTrue, reason: 'Should return same singleton instance');

      // Store data with one instance
      await instance1.storePendingPayment(
        transactionId: 'SINGLETON_TEST_123',
        merchantTxnId: 'MERCHANT_SINGLETON_123',
        amount: 25.0,
        paymentParams: {'singleton': 'test'},
      );

      // Retrieve with another instance
      final hasPending = await instance2.hasPendingPayment();
      expect(hasPending, isTrue, reason: 'Data should be accessible from any instance');

      print('✅ TEST 6 PASSED: Singleton instance consistency');
    });
  });
}

/// Helper function to run comprehensive PayU lifecycle test
Future<void> runComprehensivePayULifecycleTest() async {
  print('🔔 ===== STARTING COMPREHENSIVE PAYU LIFECYCLE TEST =====');
  print('🔔 Testing PayU payment lifecycle management');
  print('🔔 Timestamp: ${DateTime.now().toIso8601String()}');
  
  try {
    // Initialize SharedPreferences for testing
    SharedPreferences.setMockInitialValues({});
    
    final lifecycleManager = PayULifecycleManager.instance;
    
    print('\n🔔 === Testing payment storage and retrieval ===');
    
    // Test storing payment data
    await lifecycleManager.storePendingPayment(
      transactionId: 'CMD_TEST_${DateTime.now().millisecondsSinceEpoch}',
      merchantTxnId: 'MERCHANT_CMD_TEST',
      amount: 150.0,
      paymentParams: {
        'txnid': 'CMD_TEST_${DateTime.now().millisecondsSinceEpoch}',
        'amount': '150.0',
        'productinfo': 'Command Line Test',
        'firstname': 'CMD Test User',
        'email': '<EMAIL>',
      },
    );
    
    final hasPending = await lifecycleManager.hasPendingPayment();
    print('🔔 Pending payment stored: $hasPending');
    
    final paymentData = await lifecycleManager.getPendingPaymentData();
    print('🔔 Payment data retrieved: ${paymentData != null}');
    
    // Test app resume handling
    print('\n🔔 === Testing app resume handling ===');
    final resumeResult = await lifecycleManager.handleAppResume();
    print('🔔 App resume result: ${resumeResult?.type ?? "null"}');
    
    // Test payment completion
    print('\n🔔 === Testing payment completion ===');
    final testResult = PayUPaymentResult.success({'status': 'success', 'amount': '150.0'});
    await lifecycleManager.markPaymentCompleted(testResult);
    
    final hasPendingAfter = await lifecycleManager.hasPendingPayment();
    print('🔔 Pending payment after completion: $hasPendingAfter');
    
    // Summary
    print('\n🔔 ===== TEST SUMMARY =====');
    print('🔔 Payment storage: ${hasPending ? "✅ SUCCESS" : "❌ FAILED"}');
    print('🔔 Data retrieval: ${paymentData != null ? "✅ SUCCESS" : "❌ FAILED"}');
    print('🔔 Payment completion: ${!hasPendingAfter ? "✅ SUCCESS" : "❌ FAILED"}');
    
    if (hasPending && paymentData != null && !hasPendingAfter) {
      print('🔔 🎉 ALL PAYU LIFECYCLE TESTS SUCCESSFUL! 🎉');
    } else {
      print('🔔 ⚠️ Some tests failed - check logs above');
    }
    
  } catch (e) {
    print('🔔 ❌ COMPREHENSIVE LIFECYCLE TEST FAILED: $e');
    rethrow;
  }
}
